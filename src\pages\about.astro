---
import Layout from '../layouts/Layout.astro';
import AboutTimeline from '../components/AboutTimeline.astro';
import StoryCard from '../components/StoryCard.astro';
import { getEntry } from 'astro:content';
import { siteConfig } from '../config/site';

const aboutData = await getEntry('about', 'profile');
const { title, timeline = [], stories = [] } = aboutData?.data || {};
---

<Layout title={`About | ${siteConfig.seo.title}`}>
  <section class="about hero-refined bg-gradient-to-br from-background-light via-background-light-secondary to-secondary-50/30 dark:from-background-dark dark:via-background-dark-secondary dark:to-secondary-950/30 relative overflow-hidden">
    <!-- Sophisticated background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-1/4 left-1/4 w-80 h-80 bg-gradient-to-br from-primary-300/6 to-accent-300/4 dark:from-primary-600/8 dark:to-accent-600/6 rounded-full blur-3xl animate-float"></div>
      <div class="absolute bottom-1/3 right-1/4 w-96 h-96 bg-gradient-to-br from-accent-300/4 to-primary-300/6 dark:from-accent-600/6 dark:to-primary-600/8 rounded-full blur-3xl animate-float" style="animation-delay: 2s; animation-duration: 5s;"></div>
      
      <!-- Refined texture overlay -->
      <div class="absolute inset-0 opacity-20 dark:opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;80&quot; height=&quot;80&quot; viewBox=&quot;0 0 80 80&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23a19e92&quot; fill-opacity=&quot;0.03&quot;%3E%3Ccircle cx=&quot;40&quot; cy=&quot;40&quot; r=&quot;0.8&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="hero-title-refined animate-fade-in">
        <h1 class="heading-xl text-secondary-800 dark:text-secondary-200 mb-6 relative">
          {title}
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-600 rounded"></span>
        </h1>
      </div>
      
      <!-- Career Timeline Section -->
      <div class="mb-20">
        <div class="text-center mb-16">
          <h2 class="heading-lg text-text-light dark:text-text-dark mb-4 relative">
            My Journey
            <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-600 rounded"></span>
          </h2>
          <p class="text-lg text-text-light-muted dark:text-text-dark-muted max-w-2xl mx-auto">
            From backend specialist to full-stack delivery - here's how my passion for building complete solutions has evolved over the years.
          </p>
        </div>
        
        {timeline && timeline.length > 0 && (
          <AboutTimeline timeline={timeline} />
        )}
      </div>

      <!-- Story Cards Section -->
      {stories && stories.length > 0 && (
        <div class="space-y-12">
          {stories.map((story, index) => (
            <StoryCard 
              title={story.title}
              content={story.content}
              icon={story.icon}
              highlights={story.highlights}
              className={index % 2 === 1 ? "lg:ml-12" : "lg:mr-12"}
            />
          ))}
        </div>
      )}

      <!-- Enhanced CTA section -->
      <div class="text-center mt-16">
        <div class="glass-card p-8 text-center">
          <h3 class="heading-md text-text-light dark:text-text-dark mb-6">
            Let's Work Together
          </h3>
          <p class="text-lg text-text-light-muted dark:text-text-dark-muted mb-8 max-w-2xl mx-auto">
            Ready to build something amazing? I'm always excited to discuss new projects and opportunities.
          </p>
          <div class="flex justify-center gap-4 flex-col sm:flex-row">
            <a href="/contact" class="btn-primary group text-lg px-8 py-4">
              Get In Touch
              <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </a>
            <a href="/portfolio" class="btn-secondary group text-lg px-8 py-4">
              View My Work
              <svg class="w-5 h-5 ml-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout> 
