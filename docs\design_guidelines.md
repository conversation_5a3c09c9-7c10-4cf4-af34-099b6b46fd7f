# Design Guidelines

## Brand Identity

### Personal Brand Statement
"Professional software developer with experience building scalable systems and working with technical teams"

### Voice & Tone
- Professional but approachable
- Technical but clear
- Confident but not arrogant
- Solution-oriented and practical

## Visual Design

### Color Palette (Tailwind Configuration)
- **Primary Color:** `#3a86ff` (blue-500 equivalent)
- **Secondary Color:** `#0a2463` (blue-900 equivalent)
- **Accent Color:** `#ff9e00` (orange-400 equivalent)
- **Neutral Colors:**
  - Background: `#ffffff` (white)
  - Light background: `#f8f9fa` (gray-50)
  - Border: `#e9ecef` (gray-200)
- **Text Colors:**
  - Primary text: `#2b2d42` (gray-800)
  - Light text: `#6c757d` (gray-500)

### 🆕 Project Card Theme Colors (July 2025)
Enhanced project card theming with improved visibility across light and dark modes:

#### Project Category Colors
- **AI/Developer Tools**: Purple-blue-cyan gradient
- **Full-Stack/Backend**: Orange-red-pink gradient  
- **Crypto/Blockchain**: Yellow-orange-red gradient
- **Template/SSG**: Emerald-teal-cyan gradient
- **Analytics/Data**: Cyan-blue-indigo gradient
- **DevOps/Cloud**: Blue-indigo-purple gradient

#### Opacity Settings
- **Light Mode**: Primary `/50`, Secondary `/40`, Overlay `/30`
- **Dark Mode**: Primary `/60`, Secondary `/50`, Overlay `/40`

#### Implementation
```css
/* Light mode example */
from-purple-500/50 via-blue-500/40 to-cyan-500/50

/* Dark mode example */
dark:from-purple-500/60 dark:via-blue-500/50 dark:to-cyan-500/60
```

#### Benefits
- **Improved Visibility**: Colors now properly visible in light mode
- **Balanced Contrast**: Optimal opacity for both themes
- **Category Recognition**: Visual distinction between project types
- **Accessibility**: Better contrast ratios across all themes

### Typography (Tailwind Configuration)
- **Headings:** Montserrat (`font-heading`) - Professional, modern sans-serif
- **Body Text:** Poppins (`font-sans`) - Readable, friendly sans-serif
- **Code Snippets:** Fira Code (`font-mono`) - Developer-focused monospace with ligatures
- **Font Sizes (Tailwind Classes):**
  - H1: `text-4xl md:text-5xl` (36px/48px)
  - H2: `text-2xl md:text-3xl` (24px/30px)
  - H3: `text-xl md:text-2xl` (20px/24px)
  - Body: `text-base md:text-lg` (16px/18px)
  - Small text: `text-sm` (14px)
- **Font Weights:**
  - Headings: `font-bold` (700) or `font-extrabold` (800)
  - Body: `font-normal` (400) or `font-medium` (500)
  - Emphasis: `font-semibold` (600)

### Layout Guidelines (Tailwind Implementation)
- **Design Philosophy:** Clean, minimal design with ample white space
- **Grid System:** CSS Grid and Flexbox via Tailwind utilities
- **Maximum Content Width:** `max-w-6xl` (1152px) with `container mx-auto`
- **Responsive Breakpoints (Tailwind):**
  - Mobile: Default (0px+) - `block`
  - Small: `sm:` (640px+) - `sm:flex`
  - Medium: `md:` (768px+) - `md:grid-cols-2`
  - Large: `lg:` (1024px+) - `lg:grid-cols-3`
  - Extra Large: `xl:` (1280px+) - `xl:max-w-7xl`
- **Spacing System:** Tailwind's 4px base unit (`space-y-4`, `p-6`, `m-8`)

### UI Elements (Tailwind Classes)
- **Buttons:**
  - Primary: `bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all`
  - Secondary: `border-2 border-primary text-primary hover:bg-primary hover:text-white px-6 py-3 rounded-lg font-semibold transition-all`
  - Text: `text-base` (16px), sentence case preferred
  - Interactive states: `hover:`, `focus:`, `active:` variants
  - Accessibility: `focus:ring-2 focus:ring-primary focus:ring-offset-2`

- **Cards/Containers:**
  - Base: `bg-white rounded-xl shadow-sm border border-gray-100`
  - Hover: `hover:shadow-md transition-shadow`
  - Padding: `p-6` (24px) or `p-8` (32px) for larger cards
  - Spacing: `space-y-4` for internal content

- **Navigation:**
  - Desktop: `hidden md:flex space-x-8`
  - Mobile: `md:hidden` with toggle button
  - Active state: `text-primary border-b-2 border-primary`
  - Hover: `hover:text-primary transition-colors`

### Imagery Guidelines
- Professional headshot with neutral background
- Project screenshots with consistent aspect ratios
- Simple icons for skills and technologies
- Minimal use of decorative elements

## Component Library

### Hero Section
- Full-width background (color or subtle pattern)
- Centered or left-aligned content
- Clear headline and subheading
- Optional background image or gradient

### Resources Section
- Card-based or list layout
- Category filtering options
- Thumbnail previews for videos
- Brief description for each resource
- Clear external links with proper styling

### About Section
- Professional photo
- Two-column layout on desktop
- Timeline or cards for experience

### Portfolio Section
- Consistent card layout
- Project thumbnails with hover effects
- Clear call-to-action buttons
- Technology tags for each project

### Contact Section
- Clean form design
- Clear field labels
- Validation states
- Success/error messaging

## Accessibility Guidelines

### General Principles
- Semantic HTML structure
- Proper heading hierarchy (h1, h2, h3)
- Alt text for all images
- Descriptive link text
- Keyboard navigation support

### Color Accessibility
- Minimum contrast ratio of 4.5:1 for normal text
- Minimum contrast ratio of 3:1 for large text
- Color is not the only way to convey information
- Focus indicators for interactive elements

### Typography Accessibility
- Font size minimum of 16px for body text
- Line height of 1.5 for better readability
- Adequate spacing between interactive elements
- Readable fonts across all platforms

## Performance Guidelines

### Image Optimization
- WebP format when possible
- Appropriate sizing for different screen sizes
- Lazy loading for non-critical images
- Optimized alt text

### CSS Performance
- Minimize unused CSS
- Use Tailwind's purge functionality
- Efficient selectors and specificity
- Critical CSS inlining

### JavaScript Performance
- Minimal JavaScript footprint
- Efficient event handling
- Lazy loading for non-critical features
- Modern browser feature detection

## Responsive Design

### Mobile-First Approach
- Design for mobile first, then enhance for larger screens
- Touch-friendly interface elements
- Readable text without zooming
- Appropriate button sizes for touch

### Breakpoint Strategy
- Mobile: 320px - 767px
- Tablet: 768px - 1023px
- Desktop: 1024px+
- Large Desktop: 1280px+

### Content Adaptation
- Responsive typography scaling
- Flexible layouts that work on all devices
- Optimized images for different screen densities
- Progressive disclosure for complex content

## Brand Application

### Consistency
- Consistent use of colors, fonts, and spacing
- Standardized component usage
- Regular design system updates
- Documentation of design decisions

### Flexibility
- Adaptable to different content types
- Scalable for future additions
- Maintainable code structure
- Clear component boundaries

## Future Considerations

### Scalability
- Modular design system
- Reusable components
- Efficient content management
- Easy theme customization

### Maintenance
- Regular design review cycles
- Performance monitoring
- Accessibility audits
- User feedback integration

---

Last Updated: July 2025
Version: 1.1.0 - Enhanced with project card theme colors and improved accessibility guidelines