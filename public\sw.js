// Service Worker for Portfolio Site
// Implements caching strategies for optimal performance

const CACHE_NAME = 'portfolio-site-v1.0.0';
const STATIC_CACHE = 'portfolio-static-v1.0.0';
const DYNAMIC_CACHE = 'portfolio-dynamic-v1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/about',
  '/contact',
  '/portfolio',
  '/resources',
  '/resume',
  '/manifest.json',
  // Add critical CSS and JS files here when available
];

// Assets to cache with different strategies
const CACHE_STRATEGIES = {
  // Cache first for static assets
  static: [
    /\.(css|js|woff2?|ttf|eot)$/,
    /\/images\//,
    /\/assets\//
  ],
  // Network first for HTML pages
  networkFirst: [
    /\.html$/,
    /\/$/,
    /\/portfolio\//,
    /\/about/,
    /\/contact/,
    /\/resources/,
    /\/resume/
  ],
  // Stale while revalidate for API calls (future use)
  staleWhileRevalidate: [
    /\/api\//
  ]
};

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Failed to cache static assets', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip cross-origin requests (except for fonts and images)
  if (url.origin !== location.origin && !isAsset(request.url)) {
    return;
  }
  
  event.respondWith(handleRequest(request));
});

// Handle different caching strategies
async function handleRequest(request) {
  const url = request.url;

  try {
    // Static assets - Cache First
    if (isStaticAsset(url)) {
      return await cacheFirst(request, STATIC_CACHE);
    }

    // HTML pages - Network First with theme-aware caching
    if (isHTMLPage(url)) {
      // For theme consistency, always try network first for HTML
      return await networkFirst(request, DYNAMIC_CACHE, {
        networkTimeoutMs: 3000 // Quick timeout to prevent theme flashing
      });
    }

    // API calls - Stale While Revalidate (future use)
    if (isAPICall(url)) {
      return await staleWhileRevalidate(request, DYNAMIC_CACHE);
    }

    // Default to network first
    return await networkFirst(request, DYNAMIC_CACHE);

  } catch (error) {
    console.error('Service Worker: Request failed', error);

    // Return offline page for HTML requests
    if (isHTMLPage(url)) {
      return await caches.match('/offline.html') || new Response('Offline', { status: 503 });
    }

    return new Response('Network Error', { status: 503 });
  }
}

// Cache First strategy
async function cacheFirst(request, cacheName) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  const networkResponse = await fetch(request);
  
  if (networkResponse.ok) {
    const cache = await caches.open(cacheName);
    cache.put(request, networkResponse.clone());
  }
  
  return networkResponse;
}

// Network First strategy with optional timeout
async function networkFirst(request, cacheName, options = {}) {
  const { networkTimeoutMs } = options;

  try {
    let networkPromise = fetch(request);

    // Add timeout if specified
    if (networkTimeoutMs) {
      networkPromise = Promise.race([
        networkPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Network timeout')), networkTimeoutMs)
        )
      ]);
    }

    const networkResponse = await networkPromise;

    if (networkResponse.ok) {
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.log('Network first fallback to cache for:', request.url);
    const cachedResponse = await caches.match(request);
    return cachedResponse || Promise.reject(error);
  }
}

// Stale While Revalidate strategy
async function staleWhileRevalidate(request, cacheName) {
  const cachedResponse = await caches.match(request);
  
  const networkResponsePromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      const cache = caches.open(cacheName);
      cache.then(c => c.put(request, networkResponse.clone()));
    }
    return networkResponse;
  });
  
  return cachedResponse || networkResponsePromise;
}

// Helper functions
function isStaticAsset(url) {
  return CACHE_STRATEGIES.static.some(pattern => pattern.test(url));
}

function isHTMLPage(url) {
  return CACHE_STRATEGIES.networkFirst.some(pattern => pattern.test(url));
}

function isAPICall(url) {
  return CACHE_STRATEGIES.staleWhileRevalidate.some(pattern => pattern.test(url));
}

function isAsset(url) {
  return /\.(css|js|png|jpg|jpeg|gif|svg|woff2?|ttf|eot|ico)$/.test(url);
}

// Background sync for offline actions (future enhancement)
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  // Implement background sync logic here
  console.log('Service Worker: Background sync triggered');
}
