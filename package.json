{"name": "portfolio-template", "version": "1.0.0", "description": "> 🚀 **Modern Portfolio Website** - Built with performance and professional standards in mind", "homepage": "https://github.com/yourusername/portfolio-template#readme", "bugs": {"url": "https://github.com/yourusername/portfolio-template/issues"}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/portfolio-template.git"}, "license": "MIT", "author": "", "type": "module", "main": "postcss.config.js", "directories": {"doc": "docs"}, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "check": "astro check", "perf:test": "node scripts/performance-test.js", "perf:lighthouse": "lighthouse http://localhost:4321 --output=json --output-path=./lighthouse-report.json", "perf:lighthouse:mobile": "lighthouse http://localhost:4321 --preset=mobile --output=json --output-path=./lighthouse-mobile.json", "perf:audit": "npm run build && npm run preview & sleep 5 && npm run perf:test && npm run perf:lighthouse"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^2.0.0", "@astrojs/tailwind": "^5.0.0", "@tailwindcss/typography": "^0.5.16", "astro": "^4.0.0", "sharp": "^0.34.2", "tailwindcss": "^3.4.17"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/node": "^20.16.11", "terser": "^5.43.1", "typescript": "^5.8.3"}, "optionalDependencies": {"@rollup/rollup-win32-x64-msvc": "^4.44.1"}}