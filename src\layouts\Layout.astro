---
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import PerformanceMonitor from '../components/PerformanceMonitor.astro';
import ScrollAnimations from '../components/ScrollAnimations.astro';
import { siteConfig, getSocialLinks } from '../config/site';
import '../styles/global.css';

interface Props {
  title?: string;
  description?: string;
  ogImage?: string;
}

const { 
  title = siteConfig.seo.title,
  description = siteConfig.seo.description,
  ogImage = `${siteConfig.domain}${siteConfig.seo.ogImage}`
} = Astro.props;

const socialLinks = getSocialLinks();
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Primary Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title}>
    <meta name="description" content={description}>
    <meta name="keywords" content={siteConfig.seo.keywords.join(', ')}>
    <meta name="author" content={siteConfig.name}>
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content={siteConfig.domain}>
    <meta property="og:title" content={title}>
    <meta property="og:description" content={description}>
    <meta property="og:image" content={ogImage}>

    <!-- Twitter -->
    <meta property="twitter:card" content={siteConfig.seo.twitterCard}>
    <meta property="twitter:url" content={siteConfig.domain}>
    <meta property="twitter:title" content={title}>
    <meta property="twitter:description" content={description}>
    <meta property="twitter:image" content={ogImage}>

    <!-- Canonical URL -->
    <link rel="canonical" href={siteConfig.domain}>

    <!-- Favicon -->
    <link rel="icon" href="/favicon.ico" sizes="16x16 32x32">
    <link rel="icon" href="/android-chrome-192x192.png" sizes="192x192" type="image/png">
    <link rel="icon" href="/android-chrome-512x512.png" sizes="512x512" type="image/png">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <link rel="shortcut icon" href="/favicon.ico">

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content={siteConfig.name.replace(' ', '')}>
    <meta name="theme-color" content="#ff9e00">

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://github.com">
    <link rel="dns-prefetch" href="https://linkedin.com">

    <!-- Optimized font loading with preconnect and font-display -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Critical fonts with font-display: swap for better performance -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap"></noscript>
    <noscript><link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700;800&family:Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@400;500&display=swap" rel="stylesheet"></noscript>

    <!-- Font Awesome with optimized loading -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous"></noscript>

    <!-- Structured Data -->
    <script type="application/ld+json" is:inline set:html={JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Person",
        "name": siteConfig.name,
        "jobTitle": siteConfig.jobTitle,
        "description": siteConfig.seo.description,
        "url": siteConfig.domain,
        "email": siteConfig.email,
        "image": `${siteConfig.domain}/android-chrome-512x512.png`,
        "logo": `${siteConfig.domain}/android-chrome-512x512.png`,
        "address": {
            "@type": "PostalAddress",
            "addressLocality": siteConfig.location
        },
        "sameAs": socialLinks.map(([_, url]) => url),
        "knowsAbout": siteConfig.seo.keywords
    })}>
    </script>
    <!-- Theme initialization script (must be in head to prevent FOUC) -->
    <script is:inline>
      // Initialize theme before page renders to prevent flash
      (function() {
        // Prevent multiple initializations during HMR
        if (window.__THEME_INITIALIZED__) {
          return;
        }

        // Add small delay to ensure localStorage is available
        const initTheme = () => {
          try {
            const savedTheme = localStorage.getItem('theme');
            const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const shouldUseDark = savedTheme === 'dark' || (!savedTheme && systemPrefersDark);

            // Debug logging for theme initialization
            console.log('Theme Init:', {
              savedTheme,
              systemPrefersDark,
              shouldUseDark,
              userAgent: navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Other',
              timestamp: Date.now(),
              isHMR: !!window.__THEME_INITIALIZED__
            });

            // Apply theme immediately
            document.documentElement.classList.toggle('dark', shouldUseDark);

            // Store the initial state for ThemeToggle component
            window.__INITIAL_THEME__ = {
              theme: shouldUseDark ? 'dark' : 'light',
              savedTheme: savedTheme,
              systemPrefersDark: systemPrefersDark,
              timestamp: Date.now()
            };

            // Mark as initialized
            window.__THEME_INITIALIZED__ = true;
          } catch (error) {
            console.error('Theme initialization error:', error);
            // Fallback to light theme
            document.documentElement.classList.remove('dark');
          }
        };

        // Initialize immediately if localStorage is available, otherwise wait a bit
        if (typeof Storage !== 'undefined') {
          initTheme();
        } else {
          setTimeout(initTheme, 10);
        }
      })();
    </script>
  </head>
  <body class="bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark transition-colors duration-300">
    <Header />
    <main>
      <slot />
    </main>
    <Footer />

    <!-- Performance monitoring for Core Web Vitals -->
    <PerformanceMonitor />

    <!-- Scroll animations and micro-interactions -->
    <ScrollAnimations />


    <!-- Service Worker Registration -->
    <script is:inline>
      // Register service worker for caching and offline functionality
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('Service Worker registered successfully:', registration.scope);
            })
            .catch((error) => {
              console.log('Service Worker registration failed:', error);
            });
        });
      }
    </script>
  </body>
</html> 