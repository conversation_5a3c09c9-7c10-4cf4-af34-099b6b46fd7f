---
interface Props {
  title: string;
  content: string;
  icon?: string;
  imageUrl?: string;
  highlights?: string[];
  className?: string;
}

const { 
  title, 
  content, 
  icon = "📖", 
  imageUrl, 
  highlights,
  className = ""
} = Astro.props;
---

<div class={`story-card bg-white/95 dark:bg-secondary-800/95 backdrop-blur-sm rounded-2xl p-8 lg:p-10 shadow-soft hover:shadow-medium transition-all duration-300 group hover:-translate-y-1 border border-secondary-100/50 dark:border-secondary-700/50 relative ${className}`}>
  <!-- Header with icon and title -->
  <div class="flex items-start gap-4 mb-6 relative z-10">
    {imageUrl ? (
      <div class="flex-shrink-0 w-16 h-16 rounded-2xl overflow-hidden shadow-lg">
        <img 
          src={imageUrl} 
          alt={`${title} illustration`}
          class="w-full h-full object-cover"
          loading="lazy"
        />
      </div>
    ) : (
      <div class="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 dark:from-primary-400 dark:to-primary-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-all duration-300">
        <span class="text-white text-2xl">{icon}</span>
      </div>
    )}
    
    <div class="flex-1 min-w-0">
      <h3 class="text-2xl lg:text-3xl font-bold text-text-light dark:text-text-dark font-heading mb-3 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
        {title}
      </h3>
      <div class="w-12 h-0.5 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full group-hover:w-20 transition-all duration-300"></div>
    </div>
  </div>

  <!-- Content -->
  <div class="mb-6">
    {content.split('\n\n').map((paragraph) => (
      <p class="text-text-light-muted dark:text-text-dark-muted text-base leading-relaxed mb-4 last:mb-0">
        {paragraph}
      </p>
    ))}
  </div>

  <!-- Highlights -->
  {highlights && highlights.length > 0 && (
    <div class="mt-6 pt-6 border-t border-secondary-200/60 dark:border-secondary-700/60">
      <h4 class="text-sm font-semibold text-text-light-secondary dark:text-text-dark-secondary uppercase tracking-wider mb-4 flex items-center gap-2">
        <span class="w-4 h-4 bg-primary-500 dark:bg-primary-400 rounded-sm flex items-center justify-center">
          <span class="text-white text-xs">✓</span>
        </span>
        Key Points
      </h4>
      <ul class="space-y-3">
        {highlights.map((highlight) => (
          <li class="flex items-start gap-3">
            <span class="text-primary-600 dark:text-primary-400 mt-1 font-bold text-sm flex-shrink-0">•</span>
            <span class="text-text-light-muted dark:text-text-dark-muted text-sm leading-relaxed">{highlight}</span>
          </li>
        ))}
      </ul>
    </div>
  )}
</div>

