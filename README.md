# Dynamic Portfolio Template

[![Astro](https://img.shields.io/badge/Astro-4.0+-FF5D01?style=flat&logo=astro&logoColor=white)](https://astro.build)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8+-3178C6?style=flat&logo=typescript&logoColor=white)](https://www.typescriptlang.org)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4+-06B6D4?style=flat&logo=tailwindcss&logoColor=white)](https://tailwindcss.com)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

> 🚀 **Dynamic Content Portfolio** - Fully configurable portfolio with advanced content management. Built with Astro.js, TypeScript, and Tailwind CSS

## 🌟 **NEW: Advanced Dynamic Content System**

**Zero hardcoded text!** Everything is configurable through simple configuration files:

- 🎯 **Configurable Labels** - Change all UI text from one config file
- 📝 **Content-Driven Headings** - Page titles managed in content files  
- 🎨 **Smart Fallback Projects** - Professional examples auto-load when needed
- 🌍 **Internationalization Ready** - Built for multi-language support
- 🔧 **CMS Integration Ready** - Structured content for headless CMS

## ✨ Multi-Profession Support

**One template that works for everyone!** Simply choose your profession and get a perfectly tailored portfolio:

- 👨‍💻 **Developer** - Software/Web Development
- 🎨 **Designer** - UI/UX Design  
- 📱 **Marketer** - Digital Marketing
- 💼 **Consultant** - Business Consulting
- 👩‍🏫 **Teacher** - Education/Teaching

### 🚀 5-Minute Configuration

**Step 1:** Choose your profession
```typescript
// src/config/site.ts
profession: "designer" // developer, designer, marketer, consultant, teacher
```

**Step 2:** Customize your details
```typescript
// src/config/site.ts
name: "Your Name",
email: "<EMAIL>",
jobTitle: "Your Professional Title",
location: "Your City, Country"
```

**Step 3:** Customize UI text (optional)
```typescript
// src/config/site.ts
labels: {
  navigation: {
    about: "About Me",     // ← Edit any UI text
    portfolio: "My Work"   // ← Customize button labels
  }
}
```

**Everything auto-updates:** Skills, descriptions, experience highlights, SEO keywords, navigation, buttons, and more!

## 🆕 Recent Updates (July 2025)

### Portfolio Content Overhaul
- **✅ Real Projects Added**: Replaced all fallback/example projects with actual GitHub repositories
- **✅ Enhanced Case Studies**: Added comprehensive Problem/Solution/Results sections to all portfolio projects
- **✅ Professional Structure**: Each project now follows a detailed case study format with business impact metrics
- **✅ Updated Timeline**: Project dates now reflect actual development timeline (July 2025)

### UI/UX Improvements
- **✅ Theme Color Enhancement**: Fixed project card theme color visibility in both light and dark modes
- **✅ Balanced Contrast**: Optimized opacity settings for better user experience across all themes
- **✅ Project Type Detection**: Enhanced automatic color theming based on project categories

### Content Management
- **✅ Portfolio Projects**: Added Nobi Site (template), Nobify (crypto platform), and NobiCode (AI tools)
- **✅ Structured Documentation**: Each project includes detailed technical implementation and business value sections
- **✅ Professional Presentation**: Enhanced project descriptions with measurable results and impact metrics

## About

A modern, high-performance portfolio template built with cutting-edge web technologies. This project showcases professional development practices, modern architecture patterns, and performance-first design principles.

**Perfect for:** Developers, designers, marketers, consultants, teachers, and any professional seeking to showcase their work online.

## 🚀 Quick Demo

Visit the live site: [**nobi-site.vercel.app**](https://nobi-site.vercel.app) *(Replace with your actual URL)*

### Key Highlights
- ⚡ **Lightning Fast** - Astro's zero-JS approach for optimal performance
- 🎯 **Multi-Profession** - Auto-configures for 5 different professions
- 📝 **Dynamic Content System** - Zero hardcoded text, full content management
- 🌍 **Internationalization Ready** - Configurable labels for multi-language support
- 🎨 **Smart Fallback Projects** - Professional examples when no real projects exist
- 🔧 **CMS Integration Ready** - Structured content compatible with headless CMS
- 📱 **Fully Responsive** - Perfect on desktop, tablet, and mobile
- ♿ **Accessible** - Built with accessibility best practices
- 🔍 **SEO Optimized** - Profession-specific keywords and structured data

## 📚 Documentation

- **[Quick Setup Guide](SETUP.md)** - Get started in 5 minutes
- **[Profession Guide](PROFESSION_GUIDE.md)** - Complete multi-profession documentation
- **[Quick Reference](QUICK_REFERENCE.md)** - Handy reference card
- **[Configuration Examples](CONFIG_EXAMPLES.md)** - Real-world profession examples

## ⚙️ Configuration Guide

### 🎯 Basic Configuration

**File:** `src/config/site.ts`

```typescript
export const siteConfig: SiteConfig = {
  // Personal Information (Required)
  name: "Your Full Name",
  title: "Your Name | Your Professional Title",
  description: "Your professional description",
  email: "<EMAIL>",
  domain: "https://yourdomain.com",
  
  // Professional Settings (Required)
  profession: "developer", // developer, designer, marketer, consultant, teacher
  jobTitle: "Your Professional Title",
  location: "Your City, Country",
  bio: "Brief professional bio", // Auto-generated if left default
  
  // Social Links (Optional)
  social: {
    github: "https://github.com/yourusername",
    linkedin: "https://linkedin.com/in/yourusername",
    twitter: "https://twitter.com/yourusername",
    // website, instagram, youtube also supported
  },
  
  // UI Labels (All Optional - Defaults Provided)
  labels: {
    navigation: {
      home: "Home",
      about: "About",
      portfolio: "Portfolio",
      resume: "Resume",
      resources: "Resources",
      contact: "Contact"
    },
    sections: {
      aboutMe: "About Me",
      featuredProjects: "Featured Projects",
      ctaHeading: "Ready to Build Something Amazing?",
      getInTouch: "Get in Touch"
    },
    buttons: {
      viewPortfolio: "View Portfolio",
      startConversation: "Start a Conversation",
      downloadResume: "Download Resume"
      // ... more button labels
    }
  }
};
```

### 📝 Content Configuration

#### Homepage Content
**File:** `src/content/homepage/main.mdx`

```yaml
---
title: "Homepage Content"
sections:
  aboutMe:
    heading: "About Me"           # ← Customize section headings
    subheading: "Optional subtitle"
  portfolio:
    heading: "Featured Projects"
    subheading: "Project showcase description"
  cta:
    heading: "Ready to Build Something Amazing?"
    subheading: "Call-to-action description"
hero:
  headline: "Your Professional Headline"
  subheadline: "Your Professional Subtitle"
  description: "Your professional description"
  highlights:
    - icon: "🚀"
      label: "Performance Focused"
    # ... more highlights
---
```

#### Contact Page Content
**File:** `src/content/contact/main.mdx`

```yaml
---
title: "Contact Page"
heading: "Get in Touch"          # ← Customize page heading
subheading: "Contact page description"
sections:
  email:
    title: "Email Me"            # ← Customize section titles
    description: "Email section description"
  social:
    title: "Follow Me"
    description: "Social media description"
---
```

### 🎨 Portfolio Projects

#### Adding Real Projects
**Location:** `src/content/portfolio/your-project.mdx`

```yaml
---
title: "Your Project Name"
description: "Brief project description"
publishDate: 2024-01-01
technologies: ["React", "TypeScript", "Node.js"]
tags: ["Frontend", "Full-Stack"]
featured: true
problem: "What problem does this project solve?"
github: "https://github.com/you/project"
live: "https://project-demo.com"
---

# Your Project Name

Detailed project description with markdown support...

## Features
- Feature 1
- Feature 2

## Technical Details
Implementation details...
```

#### Fallback Projects (Auto-Generated)
**Location:** `src/content/portfolio/fallback/`

Professional example projects automatically load when no real projects exist:
- E-Commerce Platform Backend
- Real-time Analytics System  
- Cloud Infrastructure Platform

**Customize fallback projects** by editing files in the `fallback/` directory.

### 🌍 Internationalization

#### Multi-Language Setup

1. **Duplicate label configurations:**
```typescript
// For English
const englishLabels = { /* ... */ };

// For Spanish  
const spanishLabels = {
  navigation: {
    home: "Inicio",
    about: "Acerca de",
    portfolio: "Portafolio"
  }
  // ... more translations
};
```

2. **Language detection logic:**
```typescript
const userLanguage = navigator.language || 'en';
const labels = userLanguage.startsWith('es') ? spanishLabels : englishLabels;
```

### 🔧 Advanced Customization

#### Custom Button Text
Change any button text through the labels configuration:
```typescript
labels: {
  buttons: {
    viewPortfolio: "See My Work",
    startConversation: "Let's Chat",
    downloadResume: "Get My Resume"
  }
}
```

#### Custom Section Headings
Modify page headings through content files:
```yaml
sections:
  aboutMe:
    heading: "Who I Am"
  portfolio:
    heading: "My Projects"
```

#### Custom Navigation Labels
Personalize navigation menu text:
```typescript
labels: {
  navigation: {
    about: "My Story",
    portfolio: "My Work",
    contact: "Let's Connect"
  }
}
```

## Project Status

**Current Phase:** ✅ **Production Ready** - Fully implemented and deployed
**Development Status:** Active development with continuous improvements

### Implementation Status
- [x] **Planning & Documentation** - Comprehensive project documentation
- [x] **Technology Stack Selection** - Modern stack implemented
- [x] **Development** - Astro.js + TypeScript + Tailwind CSS implementation
- [x] **Core Features** - Portfolio, about, contact, and resume pages
- [x] **Content Management** - MDX-based content system
- [ ] **Enhancement & Optimization** - Performance and SEO improvements
- [ ] **Testing & Deployment** - Quality assurance and production launch

### Quality Targets
- **Performance:** 95+ Lighthouse score, <1.2s load time
- **Accessibility:** WCAG 2.1 AA compliance
- **SEO:** Comprehensive optimization with structured data
- **Code Quality:** TypeScript, modern development practices

## Features & Pages

### Implemented Pages
- **🏠 Homepage** (`/`) - Hero section with professional introduction and call-to-action
- **👤 About** (`/about`) - Professional background, skills, and experience
- **💼 Portfolio** (`/portfolio`) - Project showcase with detailed case studies
- **📄 Resume** (`/resume`) - Interactive resume with downloadable PDF
- **📞 Contact** (`/contact`) - Professional contact form and social links
- **📚 Resources** (`/resources`) - Curated bookmarks and technical resources

### Technical Features
- **⚡ Performance Optimized** - Astro's zero-JS by default approach
- **🔍 SEO Ready** - Automatic sitemap generation and meta tag management
- **♿ Accessibility Compliant** - WCAG 2.1 AA standards with semantic HTML
- **📱 Mobile-First Design** - Responsive design across all devices
- **🎨 Modern UI/UX** - Clean, professional design with Tailwind CSS
- **📝 Content Management** - MDX-based content system for easy updates
- **🚀 Fast Loading** - Optimized images, minimal JavaScript, efficient CSS

### Development Features
- **🔧 TypeScript** - Full type safety and better developer experience
- **🎯 Component Architecture** - Reusable Astro components
- **🎨 Design System** - Consistent styling with Tailwind CSS
- **📦 Modern Tooling** - Vite, npm, and modern build pipeline
- **🔄 Hot Reload** - Fast development with instant updates

## Technology Stack

### Current Implementation
- **Frontend Framework:** Astro.js 4.0+ with TypeScript
- **CSS Framework:** Tailwind CSS 3.4+ with custom design system
- **Content Management:** MDX with Frontmatter for dynamic content
- **Build Tools:** Vite (integrated with Astro)
- **Package Manager:** npm for dependency management
- **Development:** TypeScript for type safety and better DX

### Deployment & Hosting
- **Hosting Platform:** Vercel (Primary) + Netlify (Backup)
- **Analytics:** Vercel Analytics + Google Analytics 4
- **Domain:** Custom domain with automatic HTTPS
- **CI/CD:** Automatic deployment from GitHub

### Technology Selection Rationale

| Aspect | Astro.js Choice | Benefits |
|--------|----------------|----------|
| **Performance** | Zero-JS by default | 95+ Lighthouse scores, <1.2s load time |
| **SEO** | Built-in SSG | Automatic sitemap, meta tags, structured data |
| **Developer Experience** | Modern tooling | Hot reload, TypeScript, component architecture |
| **Maintainability** | Component-based | Reusable components, utility-first CSS |
| **Learning Curve** | Minimal | Familiar HTML/CSS/JS syntax |
| **Portfolio Fit** | Perfect match | Designed for content-focused sites |

## Getting Started

### Prerequisites
- Node.js 18+
- npm (or pnpm)

### Development Setup
```bash
# Clone the repository
git clone https://github.com/yourusername/portfolio-template.git
cd portfolio-template

# Install dependencies
npm install

# Configure your site (IMPORTANT)
cp src/config/site.example.ts src/config/site.ts
# Edit src/config/site.ts with your personal information

# Start development server
npm run dev

# Open browser to http://localhost:4321
```

### Build & Deployment
```bash
# Build for production
npm run build

# Preview production build locally
npm run preview

# Type checking
npm run check
```

### Production Deployment

#### Netlify Deployment (Recommended)
```bash
# 1. Push to GitHub
git add .
git commit -m "Deploy to Netlify"
git push origin main

# 2. Deploy on Netlify
# - Go to netlify.com and sign in
# - Click "Add new site" → "Import an existing project"
# - Connect GitHub and select your repository
# - Build settings are auto-detected from netlify.toml
```

**Netlify Configuration:**
- **Build command:** `npm run build` (auto-detected)
- **Publish directory:** `dist` (auto-detected)
- **Node version:** 18
- **404 handling:** Redirects to index.html

**Contact Form Setup:**
- Forms work automatically with Netlify Forms
- Add `netlify` attribute to form elements
- No external service required

#### Alternative: Vercel Deployment
- **Platform:** Vercel with automatic GitHub integration
- **Features:** Custom domain, automatic HTTPS, performance monitoring
- **Build Command:** `npm run build`
- **Output Directory:** `dist/`

## 📚 Documentation

Project documentation is organized in the [`docs/`](./docs/) folder:

| Document | Description |
|----------|-------------|
| [🏗️ Technical Architecture](./docs/technical-architecture.md) | Complete technology stack, architecture decisions, and implementation details |
| [🎨 Design Guidelines](./docs/design_guidelines.md) | Visual design, branding, and Tailwind configuration |
| [📝 Content Plan](./docs/content_plan.md) | Content strategy and creation guidelines |

## Project Structure

```
nobi-site/
├── src/
│   ├── components/          # Reusable Astro components
│   ├── content/            # MDX content files
│   ├── layouts/            # Page layouts
│   ├── pages/              # Route pages
│   ├── styles/             # Global styles
│   └── utils/              # Utility functions
├── docs/                   # Project documentation
├── dist/                   # Built output (generated)
└── public/                 # Static assets
```

## Contributing

This project serves as a portfolio template and demonstration of modern web development practices. Feel free to use it as inspiration for your own portfolio projects.

### Development Guidelines
- Follow TypeScript best practices
- Use Tailwind CSS for styling
- Maintain component modularity
- Write semantic HTML
- Ensure accessibility compliance

## License

This project is open source and available under the [MIT License](LICENSE).

---

**Built with ❤️ using Astro.js, TypeScript, and Tailwind CSS**
