---
import Layout from '../layouts/Layout.astro';
import { siteConfig } from '../config/site';
import { getEntry } from 'astro:content';

const contactContent = await getEntry('contact', 'main');
const { heading, subheading, sections, form: formContent } = contactContent.data;
---

<Layout title={`Contact | ${siteConfig.seo.title}`}>
  <section class="contact hero-refined bg-gradient-to-br from-background-light via-background-light-secondary to-secondary-50/40 dark:from-background-dark dark:via-background-dark-secondary dark:to-secondary-950/40 relative overflow-hidden">
    <!-- Modern background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary-400/5 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-tl from-accent-400/5 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="hero-title-refined">
        <h1 class="heading-xl text-secondary-800 dark:text-secondary-200 mb-6 relative">
          {heading}
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded"></span>
        </h1>
        <p class="text-lg text-secondary-600 dark:text-secondary-400 max-w-2xl mx-auto leading-relaxed">{subheading}</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-start">
        <div class="contact-info space-y-8">
          <div class="glass-card p-6 rounded-2xl">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200">{sections?.email?.title || "Email Me"}</h3>
            </div>
            <p class="text-secondary-600 dark:text-secondary-400 mb-3">{sections?.email?.description || "I'll get back to you as soon as possible."}</p>
            <a href={`mailto:${siteConfig.email}`} class="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium transition-colors">{siteConfig.email}</a>
          </div>
          
          <div class="glass-card p-6 rounded-2xl">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-br from-accent-500 to-primary-500 rounded-xl flex items-center justify-center text-white">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200">{sections?.social?.title || "Follow Me"}</h3>
            </div>
            <p class="text-secondary-600 dark:text-secondary-400 mb-4">{sections?.social?.description || "Connect with me on social media."}</p>
            <div class="flex space-x-4">
              {siteConfig.social.linkedin && (<a href={siteConfig.social.linkedin} target="_blank" rel="noopener" class="group flex items-center justify-center w-12 h-12 bg-secondary-100 dark:bg-secondary-800 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-xl transition-all duration-300 hover:-translate-y-1">
                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-400 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>)}
              {siteConfig.social.github && (<a href={siteConfig.social.github} target="_blank" rel="noopener" class="group flex items-center justify-center w-12 h-12 bg-secondary-100 dark:bg-secondary-800 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-xl transition-all duration-300 hover:-translate-y-1">
                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-400 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
              </a>)}
              {siteConfig.social.dailydev && (<a href={siteConfig.social.dailydev} target="_blank" rel="noopener" class="group flex items-center justify-center w-12 h-12 bg-secondary-100 dark:bg-secondary-800 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-xl transition-all duration-300 hover:-translate-y-1" title="Backend Developer Community">
                <img src="/dailydotdev-logo.png" alt="Daily.dev" class="w-5 h-5 group-hover:opacity-80 transition-opacity" />
              </a>)}
            </div>
          </div>
        </div>

        <div class="glass-card p-8 rounded-2xl">
          <div class="flex items-center gap-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-br from-accent-500 to-primary-500 rounded-xl flex items-center justify-center text-white">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200">{sections?.form?.title || "Send Message"}</h3>
          </div>
          
          <form method="POST" action="https://formspree.io/f/YOUR_FORM_ID" class="space-y-6">
            <input type="hidden" name="_next" value="/success" />
            <div>
              <label for="name" class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">Name</label>
              <input type="text" id="name" name="name" required class="w-full px-4 py-3 bg-white dark:bg-secondary-800 border border-secondary-300 dark:border-secondary-600 text-secondary-900 dark:text-secondary-100 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 placeholder-secondary-400 dark:placeholder-secondary-500" placeholder="Your full name">
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">Email</label>
              <input type="email" id="email" name="email" required class="w-full px-4 py-3 bg-white dark:bg-secondary-800 border border-secondary-300 dark:border-secondary-600 text-secondary-900 dark:text-secondary-100 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 placeholder-secondary-400 dark:placeholder-secondary-500" placeholder="<EMAIL>">
            </div>
            <div>
              <label for="message" class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-2">Message</label>
              <textarea id="message" name="message" rows="5" required class="w-full px-4 py-3 bg-white dark:bg-secondary-800 border border-secondary-300 dark:border-secondary-600 text-secondary-900 dark:text-secondary-100 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-300 placeholder-secondary-400 dark:placeholder-secondary-500 resize-none" placeholder="Tell me about your project or idea..."></textarea>
            </div>
            <div>
              <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-600 transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-xl">
                Send Message
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
</Layout> 