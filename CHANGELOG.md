# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [1.1.0] - 2025-07-15

### Added
- **Enhanced Portfolio Structure**: Added comprehensive Problem/Solution/Results sections to all portfolio projects
- **Real GitHub Projects**: Replaced all fallback/example projects with actual GitHub repositories
- **Project Case Studies**: Added detailed technical implementation and business impact sections
- **Portfolio Projects**: Added three real projects:
  - Nobi Site - Dynamic Portfolio Template (2025-07-12)
  - Nobify - Crypto Management Platform (2025-07-15)
  - NobiCode - AI-Powered Code Review Tool (2025-07-15)
- **Enhanced Documentation**: Updated content plan with new portfolio structure guidelines

### Changed
- **Project Card Theme Colors**: Fixed visibility issues in both light and dark modes
- **Theme Color Opacity**: Balanced opacity settings for better user experience
- **Project Dates**: Updated to reflect actual development timeline
- **Portfolio Content**: Enhanced from basic descriptions to comprehensive case studies

### Improved
- **Theme Color System**: Enhanced automatic color theming based on project categories
- **Project Type Detection**: Improved categorization for better visual distinction
- **Content Management**: Added structured guidelines for portfolio project documentation
- **User Experience**: Better contrast and readability across all theme modes

### Fixed
- **Light Mode Visibility**: Theme colors now properly visible in light mode
- **Dark Mode Balance**: Maintained optimal contrast in dark mode
- **Project Card Consistency**: Uniform styling across all project cards
- **Color Blur Issue**: Resolved blurry appearance of theme colors

### Technical Details
- **Theme Color Opacity**: Light mode uses `/50`, `/40` opacity; Dark mode uses `/60`, `/50` opacity
- **Project Categories**: Added support for AI, Full-Stack, Crypto, Template, SSG, Multi-Profession tags
- **Content Structure**: Implemented Problem/Solution/Results format for all portfolio projects
- **Documentation**: Enhanced content plan with new portfolio guidelines and templates

## [1.0.0] - 2025-07-12

### Added
- **Initial Release**: Dynamic portfolio template with multi-profession support
- **Core Features**: Astro.js 4.0+ with TypeScript and Tailwind CSS
- **Dynamic Content System**: Zero hardcoded text with configurable labels
- **Multi-Profession Support**: Auto-configuration for 5 different professions
- **Fallback Projects**: Professional example projects with smart loading
- **Theme System**: Dark/light mode support with smooth transitions
- **Performance Optimization**: 95+ Lighthouse score targets
- **Testing Suite**: Comprehensive Playwright end-to-end testing
- **Documentation**: Complete setup and configuration guides

### Technical Implementation
- **Framework**: Astro.js with zero-JavaScript approach
- **Styling**: Tailwind CSS with custom design system
- **Content**: MDX-based content management
- **Type Safety**: Full TypeScript implementation
- **Testing**: Playwright for E2E testing
- **Performance**: Core Web Vitals optimization

---

## Version History

- **v1.1.0**: Enhanced portfolio with real projects and improved UI
- **v1.0.0**: Initial release with dynamic content system and multi-profession support

## Contributors

- **Nobhokleng** - Lead Developer and Project Owner

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.